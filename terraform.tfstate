{"version": 4, "terraform_version": "1.12.2", "serial": 44, "lineage": "350a2367-4e9b-3975-b672-7d6dc91670d9", "outputs": {"management_outputs": {"value": {"ama_user_assigned_identity_id": null, "auto_provisioning_id": null, "change_tracking_dcr_id": null, "data_collection_rules": {"change_tracking": null, "defender_sql": null, "vm_insights": null}, "defender_plan_ids": {"apis": null, "app_services": null, "arm": null, "containers": null, "cosmosdbs": null, "cspm": null, "dns": null, "key_vault": null, "oss_databases": null, "servers": null, "sql_server_vms": null, "sql_servers": null, "storage": null}, "defender_sql_dcr_id": null, "log_analytics_primary_shared_key": "RT38WB3dqsEUypJzp8telEnqjP8hp1Ro2Vse8I3W/9ZSdG3nVlm0tx+3ABj+ZszQOS03C4tvJqfXkot6cZyyLg==", "log_analytics_solution_ids": {"agent_health_assessment": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/AgentHealthAssessment(ewh-law-southeastasia)", "anti_malware": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/AntiMalware(ewh-law-southeastasia)", "change_tracking": null, "container_insights": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/ContainerInsights(ewh-law-southeastasia)", "security": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/Security(ewh-law-southeastasia)", "updates": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/Updates(ewh-law-southeastasia)", "vm_insights": null}, "log_analytics_workspace_customer_id": "e4d47fb5-4243-4d03-b9aa-01681d9c9357", "log_analytics_workspace_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationalInsights/workspaces/ewh-law-southeastasia", "log_analytics_workspace_name": "ewh-law-southeastasia", "resource_group_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia", "resource_group_name": "ewh-rg-management-southeastasia", "security_center_contact_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/providers/Microsoft.Security/securityContacts/default1", "sentinel_id": null, "vm_insights_dcr_id": null}, "type": ["object", {"ama_user_assigned_identity_id": "dynamic", "auto_provisioning_id": "dynamic", "change_tracking_dcr_id": "dynamic", "data_collection_rules": ["object", {"change_tracking": ["object", {"id": "dynamic", "name": "dynamic"}], "defender_sql": ["object", {"id": "dynamic", "name": "dynamic"}], "vm_insights": ["object", {"id": "dynamic", "name": "dynamic"}]}], "defender_plan_ids": ["object", {"apis": "dynamic", "app_services": "dynamic", "arm": "dynamic", "containers": "dynamic", "cosmosdbs": "dynamic", "cspm": "dynamic", "dns": "dynamic", "key_vault": "dynamic", "oss_databases": "dynamic", "servers": "dynamic", "sql_server_vms": "dynamic", "sql_servers": "dynamic", "storage": "dynamic"}], "defender_sql_dcr_id": "dynamic", "log_analytics_primary_shared_key": "string", "log_analytics_solution_ids": ["object", {"agent_health_assessment": "string", "anti_malware": "string", "change_tracking": "dynamic", "container_insights": "string", "security": "string", "updates": "string", "vm_insights": "dynamic"}], "log_analytics_workspace_customer_id": "string", "log_analytics_workspace_id": "string", "log_analytics_workspace_name": "string", "resource_group_id": "string", "resource_group_name": "string", "security_center_contact_id": "string", "sentinel_id": "dynamic", "vm_insights_dcr_id": "dynamic"}], "sensitive": true}}, "resources": [{"mode": "data", "type": "azurerm_client_config", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"client_id": "04b07795-8ddb-461a-bbee-02f9e1bf7b46", "id": "Y2xpZW50Q29uZmlncy9jbGllbnRJZD0wNGIwNzc5NS04ZGRiLTQ2MWEtYmJlZS0wMmY5ZTFiZjdiNDY7b2JqZWN0SWQ9MGUxMWQ2MWYtZWU5OS00N2EyLTkzNmMtNTlmNGRlZWY4NjI1O3N1YnNjcmlwdGlvbklkPTg2NDI4MmJkLWFmNzAtNDE5OC05YWY1LTJmZmQ3NGJkOWI1Mjt0ZW5hbnRJZD0yYzZhZTczYy1mZDMxLTRlNGEtODhmYy0yNGJjZmNiMmY1ZDA=", "object_id": "0e11d61f-ee99-47a2-936c-59f4deef8625", "subscription_id": "864282bd-af70-4198-9af5-2ffd74bd9b52", "tenant_id": "2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.connectivity", "mode": "data", "type": "azurerm_client_config", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"client_id": "04b07795-8ddb-461a-bbee-02f9e1bf7b46", "id": "Y2xpZW50Q29uZmlncy9jbGllbnRJZD0wNGIwNzc5NS04ZGRiLTQ2MWEtYmJlZS0wMmY5ZTFiZjdiNDY7b2JqZWN0SWQ9MGUxMWQ2MWYtZWU5OS00N2EyLTkzNmMtNTlmNGRlZWY4NjI1O3N1YnNjcmlwdGlvbklkPTg2NDI4MmJkLWFmNzAtNDE5OC05YWY1LTJmZmQ3NGJkOWI1Mjt0ZW5hbnRJZD0yYzZhZTczYy1mZDMxLTRlNGEtODhmYy0yNGJjZmNiMmY1ZDA=", "object_id": "0e11d61f-ee99-47a2-936c-59f4deef8625", "subscription_id": "864282bd-af70-4198-9af5-2ffd74bd9b52", "tenant_id": "2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.connectivity", "mode": "data", "type": "azurerm_resource_group", "name": "management_network", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity", "mode": "data", "type": "azurerm_virtual_network", "name": "hub_vnet", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_space": ["**********/20"], "dns_servers": [], "guid": "cc0b522d-e08a-466d-a2ba-009cc120469a", "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia", "location": "southeastasia", "name": "ewh-hub-southeastasia", "resource_group_name": "ewh-connectivity-southeastasia", "subnets": ["AzureApplicationGateway", "GatewaySubnet-Reverse", "AzureDNSResolver-Outbound", "AzureFirewallSubnet-Reverse", "AzureDNSResolver-Inbound", "GatewaySubnet", "ERGatewaySubnet", "ERGatewaySubnet-Reverse", "AzureApplicationGateway-Reverse"], "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal", "deployedBy": "terraform/azure/caf-enterprise-scale"}, "timeouts": null, "vnet_peerings": {}, "vnet_peerings_addresses": []}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.connectivity", "mode": "managed", "type": "azurerm_network_security_group", "name": "shared_services_nsg", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/networkSecurityGroups/ewh-nsg-shared-services", "location": "southeastasia", "name": "ewh-nsg-shared-services", "resource_group_name": "ewh-connectivity-southeastasia", "security_rule": [{"access": "Allow", "description": "", "destination_address_prefix": "*", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "22", "destination_port_ranges": [], "direction": "Inbound", "name": "Allow_SSH_Inbound", "priority": 1001, "protocol": "Tcp", "source_address_prefix": "**********/16", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}, {"access": "Allow", "description": "", "destination_address_prefix": "*", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "3389", "destination_port_ranges": [], "direction": "Inbound", "name": "Allow_RDP_Inbound", "priority": 1000, "protocol": "Tcp", "source_address_prefix": "**********/16", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}, {"access": "Allow", "description": "", "destination_address_prefix": "*", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "443", "destination_port_ranges": [], "direction": "Inbound", "name": "Allow_HTTPS_Inbound", "priority": 1003, "protocol": "Tcp", "source_address_prefix": "*", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}, {"access": "Allow", "description": "", "destination_address_prefix": "*", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "80", "destination_port_ranges": [], "direction": "Inbound", "name": "Allow_HTTP_Inbound", "priority": 1002, "protocol": "Tcp", "source_address_prefix": "*", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}, {"access": "<PERSON><PERSON>", "description": "", "destination_address_prefix": "*", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "*", "destination_port_ranges": [], "direction": "Inbound", "name": "Deny_All_Inbound", "priority": 4096, "protocol": "*", "source_address_prefix": "*", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}], "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azapi_resource.data_collection_rule", "module.connectivity.module.alz_connectivity.azapi_resource.diag_settings", "module.connectivity.module.alz_connectivity.azurerm_automation_account.management", "module.connectivity.module.alz_connectivity.azurerm_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_express_route_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_linked_service.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_solution.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_workspace.management", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_management_group_policy_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_management_group_subscription_association.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_set_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.connectivity.module.alz_connectivity.azurerm_public_ip.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.management", "module.connectivity.module.alz_connectivity.azurerm_resource_group.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_managed_identity_operator", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_reader", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.policy_assignment", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_subnet.connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_core", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_identity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_management", "module.connectivity.module.alz_connectivity.azurerm_user_assigned_identity.management", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_connection.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_routing_intent.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_gateway.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_peering.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_wan.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_vpn_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.data.azapi_resource.user_msi", "module.connectivity.module.alz_connectivity.data.azurerm_policy_definition.external_lookup", "module.connectivity.module.alz_connectivity.data.azurerm_policy_set_definition.external_lookup", "module.connectivity.module.alz_connectivity.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.connectivity.module.alz_connectivity.random_id.telem", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_set_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_definition", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity", "mode": "managed", "type": "azurerm_resource_group", "name": "route_tables", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-rg-route-tables-southeastasia", "location": "southeastasia", "managed_by": "", "name": "ewh-rg-route-tables-southeastasia", "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity", "mode": "managed", "type": "azurerm_route_table", "name": "management_route_table", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"bgp_route_propagation_enabled": true, "disable_bgp_route_propagation": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-rg-route-tables-southeastasia/providers/Microsoft.Network/routeTables/ewh-rt-management", "location": "southeastasia", "name": "ewh-rt-management", "resource_group_name": "ewh-rg-route-tables-southeastasia", "route": [{"address_prefix": "0.0.0.0/0", "name": "internet-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "**********/16", "name": "to-prod-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "**********/16", "name": "to-nonprod-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}], "subnets": [], "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.azurerm_resource_group.route_tables", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azapi_resource.data_collection_rule", "module.connectivity.module.alz_connectivity.azapi_resource.diag_settings", "module.connectivity.module.alz_connectivity.azurerm_automation_account.management", "module.connectivity.module.alz_connectivity.azurerm_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_express_route_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_linked_service.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_solution.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_workspace.management", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_management_group_policy_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_management_group_subscription_association.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_set_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.connectivity.module.alz_connectivity.azurerm_public_ip.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.management", "module.connectivity.module.alz_connectivity.azurerm_resource_group.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_managed_identity_operator", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_reader", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.policy_assignment", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_subnet.connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_core", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_identity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_management", "module.connectivity.module.alz_connectivity.azurerm_user_assigned_identity.management", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_connection.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_routing_intent.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_gateway.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_peering.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_wan.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_vpn_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.data.azapi_resource.user_msi", "module.connectivity.module.alz_connectivity.data.azurerm_policy_definition.external_lookup", "module.connectivity.module.alz_connectivity.data.azurerm_policy_set_definition.external_lookup", "module.connectivity.module.alz_connectivity.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.connectivity.module.alz_connectivity.random_id.telem", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_set_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_definition", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity", "mode": "managed", "type": "azurerm_route_table", "name": "nonproduction_spoke_route_table", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"bgp_route_propagation_enabled": true, "disable_bgp_route_propagation": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-rg-route-tables-southeastasia/providers/Microsoft.Network/routeTables/ewh-rt-nonproduction-spokes", "location": "southeastasia", "name": "ewh-rt-nonproduction-spokes", "resource_group_name": "ewh-rg-route-tables-southeastasia", "route": [{"address_prefix": "0.0.0.0/0", "name": "internet-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "***********/21", "name": "to-management-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "**********/16", "name": "to-prod-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "**********/16", "name": "nonprod-to-nonprod-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}], "subnets": [], "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.azurerm_resource_group.route_tables", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azapi_resource.data_collection_rule", "module.connectivity.module.alz_connectivity.azapi_resource.diag_settings", "module.connectivity.module.alz_connectivity.azurerm_automation_account.management", "module.connectivity.module.alz_connectivity.azurerm_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_express_route_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_linked_service.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_solution.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_workspace.management", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_management_group_policy_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_management_group_subscription_association.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_set_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.connectivity.module.alz_connectivity.azurerm_public_ip.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.management", "module.connectivity.module.alz_connectivity.azurerm_resource_group.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_managed_identity_operator", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_reader", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.policy_assignment", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_subnet.connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_core", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_identity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_management", "module.connectivity.module.alz_connectivity.azurerm_user_assigned_identity.management", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_connection.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_routing_intent.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_gateway.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_peering.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_wan.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_vpn_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.data.azapi_resource.user_msi", "module.connectivity.module.alz_connectivity.data.azurerm_policy_definition.external_lookup", "module.connectivity.module.alz_connectivity.data.azurerm_policy_set_definition.external_lookup", "module.connectivity.module.alz_connectivity.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.connectivity.module.alz_connectivity.random_id.telem", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_set_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_definition", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity", "mode": "managed", "type": "azurerm_route_table", "name": "production_spoke_route_table", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"bgp_route_propagation_enabled": true, "disable_bgp_route_propagation": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-rg-route-tables-southeastasia/providers/Microsoft.Network/routeTables/ewh-rt-production-spokes", "location": "southeastasia", "name": "ewh-rt-production-spokes", "resource_group_name": "ewh-rg-route-tables-southeastasia", "route": [{"address_prefix": "0.0.0.0/0", "name": "internet-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "***********/21", "name": "to-management-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "**********/16", "name": "spoke-to-spoke-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "**********/16", "name": "to-nonprod-via-firewall", "next_hop_in_ip_address": "**********", "next_hop_type": "VirtualAppliance"}], "subnets": [], "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.azurerm_resource_group.route_tables", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azapi_resource.data_collection_rule", "module.connectivity.module.alz_connectivity.azapi_resource.diag_settings", "module.connectivity.module.alz_connectivity.azurerm_automation_account.management", "module.connectivity.module.alz_connectivity.azurerm_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_express_route_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.connectivity", "module.connectivity.module.alz_connectivity.azurerm_firewall_policy.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_linked_service.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_solution.management", "module.connectivity.module.alz_connectivity.azurerm_log_analytics_workspace.management", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_management_group_policy_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_management_group_subscription_association.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_set_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone.connectivity", "module.connectivity.module.alz_connectivity.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.connectivity.module.alz_connectivity.azurerm_public_ip.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.management", "module.connectivity.module.alz_connectivity.azurerm_resource_group.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_managed_identity_operator", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.ama_reader", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.policy_assignment", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.connectivity.module.alz_connectivity.azurerm_role_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_subnet.connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_connectivity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_core", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_identity", "module.connectivity.module.alz_connectivity.azurerm_subscription_template_deployment.telemetry_management", "module.connectivity.module.alz_connectivity.azurerm_user_assigned_identity.management", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_connection.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_hub_routing_intent.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_gateway.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network_peering.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_wan.virtual_wan", "module.connectivity.module.alz_connectivity.azurerm_vpn_gateway.virtual_wan", "module.connectivity.module.alz_connectivity.data.azapi_resource.user_msi", "module.connectivity.module.alz_connectivity.data.azurerm_policy_definition.external_lookup", "module.connectivity.module.alz_connectivity.data.azurerm_policy_set_definition.external_lookup", "module.connectivity.module.alz_connectivity.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.connectivity.module.alz_connectivity.random_id.telem", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_set_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_definition", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "data", "type": "azapi_resource", "name": "user_msi", "provider": "provider[\"registry.terraform.io/azure/azapi\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "data", "type": "azurerm_policy_definition", "name": "external_lookup", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "data", "type": "azurerm_policy_set_definition", "name": "external_lookup", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azapi_resource", "name": "data_collection_rule", "provider": "provider[\"registry.terraform.io/azure/azapi\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azapi_resource", "name": "diag_settings", "provider": "provider[\"registry.terraform.io/azure/azapi\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_automation_account", "name": "management", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_dns_zone", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_express_route_gateway", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_firewall", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_firewall", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_firewall_policy", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_firewall_policy", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_log_analytics_linked_service", "name": "management", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "management", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_log_analytics_workspace", "name": "management", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group", "name": "level_1", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group", "name": "level_2", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group", "name": "level_3", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group", "name": "level_4", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group", "name": "level_5", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group", "name": "level_6", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group_policy_assignment", "name": "enterprise_scale", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_management_group_subscription_association", "name": "enterprise_scale", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_network_ddos_protection_plan", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_policy_definition", "name": "enterprise_scale", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_policy_set_definition", "name": "enterprise_scale", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_private_dns_zone", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_private_dns_zone_virtual_network_link", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_public_ip", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_resource_group", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia", "schema_version": 0, "attributes": {"id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia", "location": "southeastasia", "managed_by": "", "name": "ewh-connectivity-southeastasia", "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal", "deployedBy": "terraform/azure/caf-enterprise-scale"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_resource_group", "name": "management", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_resource_group", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_role_assignment", "name": "ama_managed_identity_operator", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_role_assignment", "name": "ama_reader", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_role_assignment", "name": "deploy_azsqldb_auditing_connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_role_assignment", "name": "enterprise_scale", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_role_assignment", "name": "policy_assignment", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_role_assignment", "name": "private_dns_zone_contributor_connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_role_definition", "name": "enterprise_scale", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_subnet", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureApplicationGateway", "schema_version": 0, "attributes": {"address_prefixes": ["**********/24"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureApplicationGateway", "name": "AzureApplicationGateway", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureApplicationGateway-Reverse", "schema_version": 0, "attributes": {"address_prefixes": ["**********/24"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureApplicationGateway-Reverse", "name": "AzureApplicationGateway-Reverse", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureDNSResolver-Inbound", "schema_version": 0, "attributes": {"address_prefixes": ["**********/27"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureDNSResolver-Inbound", "name": "AzureDNSResolver-Inbound", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureDNSResolver-Outbound", "schema_version": 0, "attributes": {"address_prefixes": ["***********/27"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureDNSResolver-Outbound", "name": "AzureDNSResolver-Outbound", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureFirewallSubnet-Reverse", "schema_version": 0, "attributes": {"address_prefixes": ["***********/26"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/AzureFirewallSubnet-Reverse", "name": "AzureFirewallSubnet-Reverse", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/ERGatewaySubnet", "schema_version": 0, "attributes": {"address_prefixes": ["************/27"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/ERGatewaySubnet", "name": "ERGatewaySubnet", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/ERGatewaySubnet-Reverse", "schema_version": 0, "attributes": {"address_prefixes": ["************/27"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/ERGatewaySubnet-Reverse", "name": "ERGatewaySubnet-Reverse", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/GatewaySubnet", "schema_version": 0, "attributes": {"address_prefixes": ["************/27"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/GatewaySubnet", "name": "GatewaySubnet", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}, {"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/GatewaySubnet-Reverse", "schema_version": 0, "attributes": {"address_prefixes": ["************/27"], "default_outbound_access_enabled": true, "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia/subnets/GatewaySubnet-Reverse", "name": "GatewaySubnet-Reverse", "private_endpoint_network_policies": "Enabled", "private_endpoint_network_policies_enabled": true, "private_link_service_network_policies_enabled": true, "resource_group_name": "ewh-connectivity-southeastasia", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "ewh-hub-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.connectivity.module.alz_connectivity.azurerm_virtual_network.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"debug_level": "", "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/providers/Microsoft.Resources/deployments/pid-97603aac-98f8-4a55-92fc-4c78378c9ba5_v6.0.0_0001_9efdeee9", "location": "southeastasia", "name": "pid-97603aac-98f8-4a55-92fc-4c78378c9ba5_v6.0.0_0001_9efdeee9", "output_content": "{\"telemetry\":{\"type\":\"String\",\"value\":\"For more information, see https://aka.ms/alz/tf/telemetry\"}}", "parameters_content": "{}", "tags": null, "template_content": "{\"$schema\":\"https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#\",\"contentVersion\":\"*******\",\"outputs\":{\"telemetry\":{\"type\":\"String\",\"value\":\"For more information, see https://aka.ms/alz/tf/telemetry\"}},\"parameters\":{},\"resources\":[],\"variables\":{}}", "template_spec_version_id": "", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "************************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.random_id.telem", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_core", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"debug_level": "", "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/providers/Microsoft.Resources/deployments/pid-36dcde81-8c33-4da0-8dc3-265381502ccb_v6.0.0_0000_9efdeee9", "location": "southeastasia", "name": "pid-36dcde81-8c33-4da0-8dc3-265381502ccb_v6.0.0_0000_9efdeee9", "output_content": "{\"telemetry\":{\"type\":\"String\",\"value\":\"For more information, see https://aka.ms/alz/tf/telemetry\"}}", "parameters_content": "{}", "tags": null, "template_content": "{\"$schema\":\"https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#\",\"contentVersion\":\"*******\",\"outputs\":{\"telemetry\":{\"type\":\"String\",\"value\":\"For more information, see https://aka.ms/alz/tf/telemetry\"}},\"parameters\":{},\"resources\":[],\"variables\":{}}", "template_spec_version_id": "", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "************************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.random_id.telem", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_identity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_management", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_user_assigned_identity", "name": "management", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_virtual_hub", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_virtual_hub_connection", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_virtual_hub_routing_intent", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_virtual_network", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia", "schema_version": 0, "attributes": {"address_space": ["**********/20"], "bgp_community": "", "ddos_protection_plan": [], "dns_servers": [], "edge_zone": "", "encryption": [], "flow_timeout_in_minutes": 0, "guid": "cc0b522d-e08a-466d-a2ba-009cc120469a", "id": "/subscriptions/864282bd-af70-4198-9af5-2ffd74bd9b52/resourceGroups/ewh-connectivity-southeastasia/providers/Microsoft.Network/virtualNetworks/ewh-hub-southeastasia", "location": "southeastasia", "name": "ewh-hub-southeastasia", "resource_group_name": "ewh-connectivity-southeastasia", "subnet": [], "tags": {"Component": "Connectivity", "Network": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Purpose": "Networking", "Traffic": "Internal", "deployedBy": "terraform/azure/caf-enterprise-scale"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_network_ddos_protection_plan.connectivity", "module.connectivity.module.alz_connectivity.azurerm_resource_group.connectivity", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_virtual_network_gateway", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_virtual_network_peering", "name": "connectivity", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_virtual_wan", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "azurerm_vpn_gateway", "name": "virtual_wan", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "random_id", "name": "telem", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"b64_std": "nv3u6Q==", "b64_url": "nv3u6Q", "byte_length": 4, "dec": "**********", "hex": "9efdeee9", "id": "nv3u6Q", "keepers": null, "prefix": null}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_management_group", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:39:33Z", "triggers": {"azurerm_management_group_level_1": "[]", "azurerm_management_group_level_2": "[]", "azurerm_management_group_level_3": "[]", "azurerm_management_group_level_4": "[]", "azurerm_management_group_level_5": "[]", "azurerm_management_group_level_6": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_policy_assignment", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:41:12Z", "triggers": {"azurerm_management_group_policy_assignment_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_management_group_policy_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_set_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_resource_group.management", "module.connectivity.module.alz_connectivity.azurerm_user_assigned_identity.management", "module.connectivity.module.alz_connectivity.data.azurerm_policy_definition.external_lookup", "module.connectivity.module.alz_connectivity.data.azurerm_policy_set_definition.external_lookup", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_set_definition", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_policy_definition", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:40:12Z", "triggers": {"azurerm_policy_definition_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_policy_set_definition", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:40:42Z", "triggers": {"azurerm_policy_set_definition_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_set_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_definition", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_role_assignment", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "0s", "destroy_duration": "0s", "id": "2025-08-26T04:41:12Z", "triggers": {"azurerm_role_assignment_enterprise_scale": "[]", "module_role_assignments_for_policy": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_management_group_policy_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_policy_set_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_resource_group.management", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_role_assignment.policy_assignment", "module.connectivity.module.alz_connectivity.azurerm_role_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.azurerm_user_assigned_identity.management", "module.connectivity.module.alz_connectivity.data.azapi_resource.user_msi", "module.connectivity.module.alz_connectivity.data.azurerm_policy_definition.external_lookup", "module.connectivity.module.alz_connectivity.data.azurerm_policy_set_definition.external_lookup", "module.connectivity.module.alz_connectivity.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_assignment", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_policy_set_definition", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_role_definition", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.connectivity.module.alz_connectivity", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_role_definition", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "60s", "destroy_duration": "0s", "id": "2025-08-26T04:40:39Z", "triggers": {"azurerm_role_definition_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.connectivity.data.azurerm_client_config.current", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_1", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_2", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_3", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_4", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_5", "module.connectivity.module.alz_connectivity.azurerm_management_group.level_6", "module.connectivity.module.alz_connectivity.azurerm_role_definition.enterprise_scale", "module.connectivity.module.alz_connectivity.time_sleep.after_azurerm_management_group", "module.management.azurerm_resource_group.management", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azapi_resource.data_collection_rule", "module.management.module.alz.azapi_resource.diag_settings", "module.management.module.alz.azurerm_automation_account.management", "module.management.module.alz.azurerm_dns_zone.connectivity", "module.management.module.alz.azurerm_express_route_gateway.virtual_wan", "module.management.module.alz.azurerm_firewall.connectivity", "module.management.module.alz.azurerm_firewall.virtual_wan", "module.management.module.alz.azurerm_firewall_policy.connectivity", "module.management.module.alz.azurerm_firewall_policy.virtual_wan", "module.management.module.alz.azurerm_log_analytics_linked_service.management", "module.management.module.alz.azurerm_log_analytics_solution.management", "module.management.module.alz.azurerm_log_analytics_workspace.management", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_management_group_subscription_association.enterprise_scale", "module.management.module.alz.azurerm_network_ddos_protection_plan.connectivity", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_private_dns_zone.connectivity", "module.management.module.alz.azurerm_private_dns_zone_virtual_network_link.connectivity", "module.management.module.alz.azurerm_public_ip.connectivity", "module.management.module.alz.azurerm_resource_group.connectivity", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_resource_group.virtual_wan", "module.management.module.alz.azurerm_role_assignment.ama_managed_identity_operator", "module.management.module.alz.azurerm_role_assignment.ama_reader", "module.management.module.alz.azurerm_role_assignment.deploy_azsqldb_auditing_connectivity", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_assignment.private_dns_zone_contributor_connectivity", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_subnet.connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_connectivity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_core", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_identity", "module.management.module.alz.azurerm_subscription_template_deployment.telemetry_management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.azurerm_virtual_hub.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_connection.virtual_wan", "module.management.module.alz.azurerm_virtual_hub_routing_intent.virtual_wan", "module.management.module.alz.azurerm_virtual_network.connectivity", "module.management.module.alz.azurerm_virtual_network_gateway.connectivity", "module.management.module.alz.azurerm_virtual_network_peering.connectivity", "module.management.module.alz.azurerm_virtual_wan.virtual_wan", "module.management.module.alz.azurerm_vpn_gateway.virtual_wan", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.random_id.telem", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_assignment", "module.management.module.alz.time_sleep.after_azurerm_role_definition", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.change_tracking", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.defender_sql", "module.management.module.azure_monitor_agent.azurerm_monitor_data_collection_rule.vm_insights", "module.management.module.azure_monitor_agent.azurerm_user_assigned_identity.ama", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.application", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_event.system", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.cpu", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.disk", "module.management.module.log_analytics.azurerm_log_analytics_datasource_windows_performance_counter.memory", "module.management.module.log_analytics.azurerm_log_analytics_solution.agent_health_assessment", "module.management.module.log_analytics.azurerm_log_analytics_solution.anti_malware", "module.management.module.log_analytics.azurerm_log_analytics_solution.change_tracking", "module.management.module.log_analytics.azurerm_log_analytics_solution.container_insights", "module.management.module.log_analytics.azurerm_log_analytics_solution.security", "module.management.module.log_analytics.azurerm_log_analytics_solution.updates", "module.management.module.log_analytics.azurerm_log_analytics_solution.vm_insights", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main", "module.management.module.log_analytics.azurerm_sentinel_log_analytics_workspace_onboarding.main", "module.management.module.security_center.azurerm_security_center_contact.main", "module.management.module.security_center.azurerm_security_center_subscription_pricing.apis", "module.management.module.security_center.azurerm_security_center_subscription_pricing.app_services", "module.management.module.security_center.azurerm_security_center_subscription_pricing.arm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.containers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cosmosdbs", "module.management.module.security_center.azurerm_security_center_subscription_pricing.cspm", "module.management.module.security_center.azurerm_security_center_subscription_pricing.dns", "module.management.module.security_center.azurerm_security_center_subscription_pricing.key_vault", "module.management.module.security_center.azurerm_security_center_subscription_pricing.oss_databases", "module.management.module.security_center.azurerm_security_center_subscription_pricing.servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_server_vms", "module.management.module.security_center.azurerm_security_center_subscription_pricing.sql_servers", "module.management.module.security_center.azurerm_security_center_subscription_pricing.storage", "module.management.module.security_center.data.azurerm_client_config.current"]}]}, {"module": "module.management", "mode": "data", "type": "azurerm_client_config", "name": "current", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"client_id": "04b07795-8ddb-461a-bbee-02f9e1bf7b46", "id": "Y2xpZW50Q29uZmlncy9jbGllbnRJZD0wNGIwNzc5NS04ZGRiLTQ2MWEtYmJlZS0wMmY5ZTFiZjdiNDY7b2JqZWN0SWQ9MGUxMWQ2MWYtZWU5OS00N2EyLTkzNmMtNTlmNGRlZWY4NjI1O3N1YnNjcmlwdGlvbklkPTY4M2JlNmE1LWI1MmMtNDQzNy05OWUyLTczOTY2NjQ0OWJkZDt0ZW5hbnRJZD0yYzZhZTczYy1mZDMxLTRlNGEtODhmYy0yNGJjZmNiMmY1ZDA=", "object_id": "0e11d61f-ee99-47a2-936c-59f4deef8625", "subscription_id": "683be6a5-b52c-4437-99e2-739666449bdd", "tenant_id": "2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.management", "mode": "managed", "type": "azurerm_resource_group", "name": "management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia", "location": "southeastasia", "managed_by": "", "name": "ewh-rg-management-southeastasia", "tags": {"ApplicationName": "Platform Infrastructure", "BackupRequired": "Yes", "BusinessUnit": "IT", "Compliance": "ISO27001", "CostCenter": "IT-001", "CreatedBy": "<EMAIL>", "DRPlan": "Yes", "DataZone": "Internal", "Environment": "Production", "ManagedBy": "Terraform", "Monitoring": "Yes", "OperationTeam": "Platform Team", "Organization": "EWH", "Owner": "<EMAIL>", "Priority": "High", "ProjectName": "EWH Landing Zone", "ResourceType": "Infrastructure"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current"]}]}, {"module": "module.management.module.alz", "mode": "data", "type": "azapi_resource", "name": "user_msi", "provider": "provider[\"registry.terraform.io/azure/azapi\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "data", "type": "azurerm_policy_definition", "name": "external_lookup", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "data", "type": "azurerm_policy_set_definition", "name": "external_lookup", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azapi_resource", "name": "data_collection_rule", "provider": "provider[\"registry.terraform.io/azure/azapi\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azapi_resource", "name": "diag_settings", "provider": "provider[\"registry.terraform.io/azure/azapi\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_automation_account", "name": "management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_dns_zone", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_express_route_gateway", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_firewall", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_firewall", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_firewall_policy", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_firewall_policy", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_log_analytics_linked_service", "name": "management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_log_analytics_workspace", "name": "management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group", "name": "level_1", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group", "name": "level_2", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group", "name": "level_3", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group", "name": "level_4", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group", "name": "level_5", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group", "name": "level_6", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group_policy_assignment", "name": "enterprise_scale", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_management_group_subscription_association", "name": "enterprise_scale", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_network_ddos_protection_plan", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_policy_definition", "name": "enterprise_scale", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_policy_set_definition", "name": "enterprise_scale", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_private_dns_zone", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_private_dns_zone_virtual_network_link", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_public_ip", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_resource_group", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_resource_group", "name": "management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_resource_group", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_role_assignment", "name": "ama_managed_identity_operator", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_role_assignment", "name": "ama_reader", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_role_assignment", "name": "deploy_azsqldb_auditing_connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_role_assignment", "name": "enterprise_scale", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_role_assignment", "name": "policy_assignment", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_role_assignment", "name": "private_dns_zone_contributor_connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_role_definition", "name": "enterprise_scale", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_subnet", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_core", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"debug_level": "", "id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/providers/Microsoft.Resources/deployments/pid-36dcde81-8c33-4da0-8dc3-265381502ccb_v6.0.0_0000_4f3b34bb", "location": "southeastasia", "name": "pid-36dcde81-8c33-4da0-8dc3-265381502ccb_v6.0.0_0000_4f3b34bb", "output_content": "{\"telemetry\":{\"type\":\"String\",\"value\":\"For more information, see https://aka.ms/alz/tf/telemetry\"}}", "parameters_content": "{}", "tags": null, "template_content": "{\"$schema\":\"https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#\",\"contentVersion\":\"*******\",\"outputs\":{\"telemetry\":{\"type\":\"String\",\"value\":\"For more information, see https://aka.ms/alz/tf/telemetry\"}},\"parameters\":{},\"resources\":[],\"variables\":{}}", "template_spec_version_id": "", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "************************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_client_config.current", "module.management.module.alz.random_id.telem"]}]}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_identity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_subscription_template_deployment", "name": "telemetry_management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_user_assigned_identity", "name": "management", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_virtual_hub", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_virtual_hub_connection", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_virtual_hub_routing_intent", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_virtual_network", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_virtual_network_gateway", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_virtual_network_peering", "name": "connectivity", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_virtual_wan", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "azurerm_vpn_gateway", "name": "virtual_wan", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.alz", "mode": "managed", "type": "random_id", "name": "telem", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"b64_std": "Tzs0uw==", "b64_url": "Tzs0uw", "byte_length": 4, "dec": "**********", "hex": "4f3b34bb", "id": "Tzs0uw", "keepers": null, "prefix": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.management.module.alz", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_management_group", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:37:33Z", "triggers": {"azurerm_management_group_level_1": "[]", "azurerm_management_group_level_2": "[]", "azurerm_management_group_level_3": "[]", "azurerm_management_group_level_4": "[]", "azurerm_management_group_level_5": "[]", "azurerm_management_group_level_6": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6"]}]}, {"module": "module.management.module.alz", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_policy_assignment", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:39:03Z", "triggers": {"azurerm_management_group_policy_assignment_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition"]}]}, {"module": "module.management.module.alz", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_policy_definition", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:38:03Z", "triggers": {"azurerm_policy_definition_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.time_sleep.after_azurerm_management_group"]}]}, {"module": "module.management.module.alz", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_policy_set_definition", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": "0s", "id": "2025-08-26T04:38:33Z", "triggers": {"azurerm_policy_set_definition_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_definition"]}]}, {"module": "module.management.module.alz", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_role_assignment", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "0s", "destroy_duration": "0s", "id": "2025-08-26T04:39:03Z", "triggers": {"azurerm_role_assignment_enterprise_scale": "[]", "module_role_assignments_for_policy": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_management_group_policy_assignment.enterprise_scale", "module.management.module.alz.azurerm_policy_definition.enterprise_scale", "module.management.module.alz.azurerm_policy_set_definition.enterprise_scale", "module.management.module.alz.azurerm_resource_group.management", "module.management.module.alz.azurerm_role_assignment.enterprise_scale", "module.management.module.alz.azurerm_role_assignment.policy_assignment", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.azurerm_user_assigned_identity.management", "module.management.module.alz.data.azapi_resource.user_msi", "module.management.module.alz.data.azurerm_policy_definition.external_lookup", "module.management.module.alz.data.azurerm_policy_set_definition.external_lookup", "module.management.module.alz.module.role_assignments_for_policy.azurerm_role_assignment.for_policy", "module.management.module.alz.time_sleep.after_azurerm_management_group", "module.management.module.alz.time_sleep.after_azurerm_policy_assignment", "module.management.module.alz.time_sleep.after_azurerm_policy_definition", "module.management.module.alz.time_sleep.after_azurerm_policy_set_definition", "module.management.module.alz.time_sleep.after_azurerm_role_definition"]}]}, {"module": "module.management.module.alz", "mode": "managed", "type": "time_sleep", "name": "after_azurerm_role_definition", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"schema_version": 0, "attributes": {"create_duration": "60s", "destroy_duration": "0s", "id": "2025-08-26T04:38:33Z", "triggers": {"azurerm_role_definition_enterprise_scale": "[]"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["data.azurerm_client_config.current", "module.management.data.azurerm_client_config.current", "module.management.module.alz.azurerm_management_group.level_1", "module.management.module.alz.azurerm_management_group.level_2", "module.management.module.alz.azurerm_management_group.level_3", "module.management.module.alz.azurerm_management_group.level_4", "module.management.module.alz.azurerm_management_group.level_5", "module.management.module.alz.azurerm_management_group.level_6", "module.management.module.alz.azurerm_role_definition.enterprise_scale", "module.management.module.alz.time_sleep.after_azurerm_management_group"]}]}, {"module": "module.management.module.azure_monitor_agent", "mode": "managed", "type": "azurerm_monitor_data_collection_rule", "name": "change_tracking", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.azure_monitor_agent", "mode": "managed", "type": "azurerm_monitor_data_collection_rule", "name": "defender_sql", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.azure_monitor_agent", "mode": "managed", "type": "azurerm_monitor_data_collection_rule", "name": "vm_insights", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.azure_monitor_agent", "mode": "managed", "type": "azurerm_user_assigned_identity", "name": "ama", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_datasource_windows_event", "name": "application", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_datasource_windows_event", "name": "system", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_datasource_windows_performance_counter", "name": "cpu", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_datasource_windows_performance_counter", "name": "disk", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_datasource_windows_performance_counter", "name": "memory", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "agent_health_assessment", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/AgentHealthAssessment(ewh-law-southeastasia)", "location": "southeastasia", "plan": [{"name": "AgentHealthAssessment(ewh-law-southeastasia)", "product": "OMSGallery/AgentHealthAssessment", "promotion_code": "", "publisher": "Microsoft"}], "resource_group_name": "ewh-rg-management-southeastasia", "solution_name": "AgentHealthAssessment", "tags": {"ApplicationName": "Platform Infrastructure", "BackupRequired": "Yes", "BusinessUnit": "IT", "Compliance": "ISO27001", "CostCenter": "IT-001", "CreatedBy": "<EMAIL>", "DRPlan": "Yes", "DataZone": "Internal", "Environment": "Production", "ManagedBy": "Terraform", "Monitoring": "Yes", "OperationTeam": "Platform Team", "Organization": "EWH", "Owner": "<EMAIL>", "Priority": "High", "ProjectName": "EWH Landing Zone", "ResourceType": "Infrastructure"}, "timeouts": null, "workspace_name": "ewh-law-southeastasia", "workspace_resource_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationalInsights/workspaces/ewh-law-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main"]}]}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "anti_malware", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/AntiMalware(ewh-law-southeastasia)", "location": "southeastasia", "plan": [{"name": "AntiMalware(ewh-law-southeastasia)", "product": "OMSGallery/AntiMalware", "promotion_code": "", "publisher": "Microsoft"}], "resource_group_name": "ewh-rg-management-southeastasia", "solution_name": "AntiMalware", "tags": {"ApplicationName": "Platform Infrastructure", "BackupRequired": "Yes", "BusinessUnit": "IT", "Compliance": "ISO27001", "CostCenter": "IT-001", "CreatedBy": "<EMAIL>", "DRPlan": "Yes", "DataZone": "Internal", "Environment": "Production", "ManagedBy": "Terraform", "Monitoring": "Yes", "OperationTeam": "Platform Team", "Organization": "EWH", "Owner": "<EMAIL>", "Priority": "High", "ProjectName": "EWH Landing Zone", "ResourceType": "Infrastructure"}, "timeouts": null, "workspace_name": "ewh-law-southeastasia", "workspace_resource_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationalInsights/workspaces/ewh-law-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main"]}]}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "change_tracking", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "container_insights", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/ContainerInsights(ewh-law-southeastasia)", "location": "southeastasia", "plan": [{"name": "ContainerInsights(ewh-law-southeastasia)", "product": "OMSGallery/ContainerInsights", "promotion_code": "", "publisher": "Microsoft"}], "resource_group_name": "ewh-rg-management-southeastasia", "solution_name": "ContainerInsights", "tags": {"ApplicationName": "Platform Infrastructure", "BackupRequired": "Yes", "BusinessUnit": "IT", "Compliance": "ISO27001", "CostCenter": "IT-001", "CreatedBy": "<EMAIL>", "DRPlan": "Yes", "DataZone": "Internal", "Environment": "Production", "ManagedBy": "Terraform", "Monitoring": "Yes", "OperationTeam": "Platform Team", "Organization": "EWH", "Owner": "<EMAIL>", "Priority": "High", "ProjectName": "EWH Landing Zone", "ResourceType": "Infrastructure"}, "timeouts": null, "workspace_name": "ewh-law-southeastasia", "workspace_resource_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationalInsights/workspaces/ewh-law-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main"]}]}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "security", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/Security(ewh-law-southeastasia)", "location": "southeastasia", "plan": [{"name": "Security(ewh-law-southeastasia)", "product": "OMSGallery/Security", "promotion_code": "", "publisher": "Microsoft"}], "resource_group_name": "ewh-rg-management-southeastasia", "solution_name": "Security", "tags": {"ApplicationName": "Platform Infrastructure", "BackupRequired": "Yes", "BusinessUnit": "IT", "Compliance": "ISO27001", "CostCenter": "IT-001", "CreatedBy": "<EMAIL>", "DRPlan": "Yes", "DataZone": "Internal", "Environment": "Production", "ManagedBy": "Terraform", "Monitoring": "Yes", "OperationTeam": "Platform Team", "Organization": "EWH", "Owner": "<EMAIL>", "Priority": "High", "ProjectName": "EWH Landing Zone", "ResourceType": "Infrastructure"}, "timeouts": null, "workspace_name": "ewh-law-southeastasia", "workspace_resource_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationalInsights/workspaces/ewh-law-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main"]}]}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "updates", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationsManagement/solutions/Updates(ewh-law-southeastasia)", "location": "southeastasia", "plan": [{"name": "Updates(ewh-law-southeastasia)", "product": "OMSGallery/Updates", "promotion_code": "", "publisher": "Microsoft"}], "resource_group_name": "ewh-rg-management-southeastasia", "solution_name": "Updates", "tags": {"ApplicationName": "Platform Infrastructure", "BackupRequired": "Yes", "BusinessUnit": "IT", "Compliance": "ISO27001", "CostCenter": "IT-001", "CreatedBy": "<EMAIL>", "DRPlan": "Yes", "DataZone": "Internal", "Environment": "Production", "ManagedBy": "Terraform", "Monitoring": "Yes", "OperationTeam": "Platform Team", "Organization": "EWH", "Owner": "<EMAIL>", "Priority": "High", "ProjectName": "EWH Landing Zone", "ResourceType": "Infrastructure"}, "timeouts": null, "workspace_name": "ewh-law-southeastasia", "workspace_resource_id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationalInsights/workspaces/ewh-law-southeastasia"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management", "module.management.module.log_analytics.azurerm_log_analytics_workspace.main"]}]}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_solution", "name": "vm_insights", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_log_analytics_workspace", "name": "main", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 3, "attributes": {"allow_resource_only_permissions": true, "cmk_for_query_forced": false, "daily_quota_gb": -1, "data_collection_rule_id": "", "id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/resourceGroups/ewh-rg-management-southeastasia/providers/Microsoft.OperationalInsights/workspaces/ewh-law-southeastasia", "identity": [], "immediate_data_purge_on_30_days_enabled": false, "internet_ingestion_enabled": true, "internet_query_enabled": true, "local_authentication_disabled": false, "location": "southeastasia", "name": "ewh-law-southeastasia", "primary_shared_key": "RT38WB3dqsEUypJzp8telEnqjP8hp1Ro2Vse8I3W/9ZSdG3nVlm0tx+3ABj+ZszQOS03C4tvJqfXkot6cZyyLg==", "reservation_capacity_in_gb_per_day": null, "resource_group_name": "ewh-rg-management-southeastasia", "retention_in_days": 60, "secondary_shared_key": "75Uiofet2cVEhxVCXzhvcuHFruAWapKju0bS7FesG/R4k/y9yJBIB1DHmDAEbjXlbhr9EyCA511+RXLQWcu2tw==", "sku": "PerGB2018", "tags": {"ApplicationName": "Platform Infrastructure", "BackupRequired": "Yes", "BusinessUnit": "IT", "Compliance": "ISO27001", "CostCenter": "IT-001", "CreatedBy": "<EMAIL>", "DRPlan": "Yes", "DataZone": "Internal", "Environment": "Production", "ManagedBy": "Terraform", "Monitoring": "Yes", "OperationTeam": "Platform Team", "Organization": "EWH", "Owner": "<EMAIL>", "Priority": "High", "ProjectName": "EWH Landing Zone", "ResourceType": "Infrastructure"}, "timeouts": null, "workspace_id": "e4d47fb5-4243-4d03-b9aa-01681d9c9357"}, "sensitive_attributes": [[{"type": "get_attr", "value": "primary_shared_key"}], [{"type": "get_attr", "value": "secondary_shared_key"}]], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIzIn0=", "dependencies": ["data.azurerm_client_config.current", "module.management.azurerm_resource_group.management"]}]}, {"module": "module.management.module.log_analytics", "mode": "managed", "type": "azurerm_sentinel_log_analytics_workspace_onboarding", "name": "main", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "data", "type": "azurerm_client_config", "name": "current", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"client_id": "04b07795-8ddb-461a-bbee-02f9e1bf7b46", "id": "Y2xpZW50Q29uZmlncy9jbGllbnRJZD0wNGIwNzc5NS04ZGRiLTQ2MWEtYmJlZS0wMmY5ZTFiZjdiNDY7b2JqZWN0SWQ9MGUxMWQ2MWYtZWU5OS00N2EyLTkzNmMtNTlmNGRlZWY4NjI1O3N1YnNjcmlwdGlvbklkPTY4M2JlNmE1LWI1MmMtNDQzNy05OWUyLTczOTY2NjQ0OWJkZDt0ZW5hbnRJZD0yYzZhZTczYy1mZDMxLTRlNGEtODhmYy0yNGJjZmNiMmY1ZDA=", "object_id": "0e11d61f-ee99-47a2-936c-59f4deef8625", "subscription_id": "683be6a5-b52c-4437-99e2-739666449bdd", "tenant_id": "2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_contact", "name": "main", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"alert_notifications": true, "alerts_to_admins": true, "email": "<EMAIL>", "id": "/subscriptions/683be6a5-b52c-4437-99e2-739666449bdd/providers/Microsoft.Security/securityContacts/default1", "name": "default1", "phone": "", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfX0=", "dependencies": ["data.azurerm_client_config.current"]}]}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "apis", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "app_services", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "arm", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "containers", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "cosmosdbs", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "cspm", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "dns", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "key_vault", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "oss_databases", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "servers", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "sql_server_vms", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "sql_servers", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}, {"module": "module.management.module.security_center", "mode": "managed", "type": "azurerm_security_center_subscription_pricing", "name": "storage", "provider": "module.management.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": []}], "check_results": [{"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_not_enforced_replacement", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.policy_non_compliance_message_not_enforced_replacement", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.subscription_id", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.connectivity_resources.var.subscription_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_not_enforced_replacement", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_not_enforced_replacement", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.subscription_id", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.management_resources.var.subscription_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.root_name", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.root_name", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.custom_landing_zones", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.custom_landing_zones", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.root_parent_id", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.root_parent_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.root_id", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.management_resources.var.root_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.log_analytics.var.sku", "status": "pass", "objects": [{"object_addr": "module.management.module.log_analytics.var.sku", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.create_duration_delay", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.create_duration_delay", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.root_id", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.connectivity_resources.var.root_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_default", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.policy_non_compliance_message_default", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.root_id", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.root_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.var.root_id", "status": "pass", "objects": [{"object_addr": "module.management.var.root_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_connectivity", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_connectivity", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.resource_suffix", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.resource_suffix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.subscription_id", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.subscription_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.identity_resources.var.root_id", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.identity_resources.var.root_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_group_archetypes.var.root_id", "status": "pass", "objects": null}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.destroy_duration_delay", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.destroy_duration_delay", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.identity_resources.var.root_id", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.identity_resources.var.root_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.resource_suffix", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.management_resources.var.resource_suffix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.resource_prefix", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.management_resources.var.resource_prefix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_management", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_management", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_enforced_replacement", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.policy_non_compliance_message_enforced_replacement", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.create_duration_delay", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.create_duration_delay", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_group_archetypes.var.root_id", "status": "pass", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.subscription_id_management", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.subscription_id_management", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.subscription_id_connectivity", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.subscription_id_connectivity", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_group_archetypes.var.scope_id", "status": "pass", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.destroy_duration_delay", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.destroy_duration_delay", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.log_analytics.var.retention_in_days", "status": "pass", "objects": [{"object_addr": "module.management.module.log_analytics.var.retention_in_days", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.root_parent_id", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.root_parent_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.resource_suffix", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.connectivity_resources.var.resource_suffix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.subscription_id_identity", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.subscription_id_identity", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_group_archetypes.var.scope_id", "status": "pass", "objects": null}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.resource_prefix", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.connectivity_resources.var.resource_prefix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_identity", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.subscription_id_identity", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_enforcement_placeholder", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_enforcement_placeholder", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.management_resources.var.custom_settings_by_resource_type", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.management_resources.var.custom_settings_by_resource_type", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.subscription_id", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.subscription_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.module.connectivity_resources.var.custom_settings_by_resource_type", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.module.connectivity_resources.var.custom_settings_by_resource_type", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.root_id", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.root_id", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.policy_non_compliance_message_enforcement_placeholder", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.policy_non_compliance_message_enforcement_placeholder", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.root_name", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.root_name", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.resource_prefix", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.resource_prefix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_enforced_replacement", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_enforced_replacement", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.module.alz.var.custom_landing_zones", "status": "pass", "objects": [{"object_addr": "module.management.module.alz.var.custom_landing_zones", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.resource_suffix", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.resource_suffix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.custom_settings_by_resource_type", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.custom_settings_by_resource_type", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.management.var.subscription_id_management", "status": "pass", "objects": [{"object_addr": "module.management.var.subscription_id_management", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_default", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.var.policy_non_compliance_message_default", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.resource_prefix", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.management_resources.var.resource_prefix", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.custom_settings_by_resource_type", "status": "pass", "objects": [{"object_addr": "module.connectivity.module.alz_connectivity.module.connectivity_resources.var.custom_settings_by_resource_type", "status": "pass"}]}]}